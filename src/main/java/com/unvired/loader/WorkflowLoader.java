package com.unvired.loader;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FilenameFilter;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.util.Map;
import java.util.Scanner;

import org.apache.tomcat.util.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.*;
import org.springframework.boot.autoconfigure.*;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

@SpringBootApplication
public class WorkflowLoader implements CommandLineRunner {

	private static Logger logger = LoggerFactory.getLogger(SpringApplication.class);
	private String company, user, password, mode, umpUrl;

	public static void main(String[] args) {
		if (args.length >= 5) {
			SpringApplication.run(WorkflowLoader.class, args);
		} else {
			System.out.println("Usage: java -jar workjflowloader.jar http(s)://umpurl mode company user password foldername");
			System.out.println("--- OR ---");
			System.out.println("Usage: java -jar workjflowloader.jar http(s)://umpurl mode company user foldername");
			System.out.println("Password will be accepted at user prompt");
			System.out.println("Mode values can be: custom - file or folder provided");
			System.out.println("                  : standard - standard flows and operations from the folder provided");
		}
	}

	String stdOpns[] = {"ads.sl", "asyncsubflow.sl", "calculate.sl", "csvparser.sl", "jsonparser.sl", "query.sl", "storedproc.sl",
						"filestore.sl", "flowresult.sl", "formapprovaldecision.sl", "formresultinternal.sl", "ftp.sl",
						"response.sl", "odata.sl", "execjava.sl", "printtext.sl", 
						"http.sl", "condition.sl", "execjavascript.sl", "execpython.sl", "sap.sl", "email.sl"};
	String stdFlows[] = {"contact.sl", "formalert.sl", "formarchive.sl", "formassign.sl", "approvalworkflow.sl",
	                    "formcreate.sl", "formpdf.sl", "formread.sl", "formsearch.sl", "formshare.sl", 
						"formupdate.sl", "masterdatareload.sl", "masterdataread.sl", "masterdataupdate.sl"};

	@Override
	public void run(String... args) throws Exception {
		umpUrl = args[0];
		if (!umpUrl.endsWith("/"))
			umpUrl = umpUrl.concat("/");
		if (!umpUrl.endsWith("/UMP/"))
			umpUrl = umpUrl.concat("UMP/");

		mode = args[1]; 
		company = args[2].toUpperCase();
		user = args[3];
		String folder = "";
		password = "";
		
		if (args.length == 5) {
			folder = args[4];
		}
		else {
			password = args[4];
			folder = args[5];
		}

		System.out.println();System.out.println();
		System.out.println("**************************************************************************");

		if (!new File(folder).exists()) {
			System.out.println("ERROR: Folder/File: " + folder + " does not exist.");
			return;
		}

		if (!new File(folder).isFile()) {
			if (!folder.endsWith(File.separator))
				folder = folder.concat(File.separator);
			if (!folder.endsWith("unvired" + File.separator))
				folder = folder.concat("unvired" + File.separator);
		}

		if (password.isEmpty()) {
			password = new String(System.console().readPassword("Please enter your password: "));
		}

		System.out.println("UMP URL: " + umpUrl);
		System.out.println("Company: " + company);
		System.out.println("User: " + user);
		System.out.println("Mode: " + mode);
		System.out.println("Content Folder: " + folder);
		System.out.println("");

		if (new File(folder).exists()) {
			// Make a get Applicaiton call and check if the authentication is working
			RestTemplate restTemplate = new RestTemplate();
			HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
			restTemplate.setRequestFactory(requestFactory);

			ResponseEntity<Map> entity = restTemplate.exchange(umpUrl + "API/v2/applications/DIGITAL_FORMS", HttpMethod.GET, new HttpEntity<>(createHeaders(company, user, password)), Map.class);	
			if (entity.getStatusCode() == HttpStatus.OK)
			{
				if ("custom".equalsIgnoreCase(mode)) {
					System.out.println("***** Procesing CUSTOM");

					if (new File(folder).isFile()) {
						uploadOpnOrFlow(restTemplate, folder);
					}
					else {
						// Start loading all operations
						FilenameFilter filter = new FilenameFilter() {
							@Override
							public boolean accept(File f, String name) {
								return name.endsWith(".sl");
							}
						};
						File operations = new File(folder + "operation");
						String [] files = operations.list(filter);
						for (String file: files) {
							String fullPath = folder + "operation" + File.separator + file;
							if (!uploadOpnOrFlow(restTemplate, fullPath))
								System.out.println("******* Invalid Workflow: " + fullPath);
						}

						// Start loading all workflows and system properties
						File workflows = new File(folder + "flow");
						files = workflows.list(filter);
						for (String file: files) {
							String fullPath = folder + "flow" + File.separator + file;
							if (!uploadOpnOrFlow(restTemplate, fullPath))
								System.out.println("******* Invalid Workflow: " + fullPath);
						}
					}
				}
				else {
					System.out.println("***** Processing STANDARD");

					for (String file: stdOpns) {
						String fullPath = folder + "operation" + File.separator + file;
						if (!uploadOpnOrFlow(restTemplate, fullPath))
							System.out.println("******* Invalid Workflow: " + fullPath);
					}

					// Start loading all workflows and system properties
					for (String file: stdFlows) {
						String fullPath = folder + "flow" + File.separator + file;
						if (!uploadOpnOrFlow(restTemplate, fullPath))
							System.out.println("******* Invalid Workflow: " + fullPath);
					}
				}
			}

		} else {
			System.out.println("Invalid folder: " + args[3] + "  Has to eo point containing content for unvired");
		}
		System.out.println("**************************************************************************");
		System.out.println();
		System.exit(0);
	}

	HttpHeaders createHeaders(String company, String username, String password) throws UnsupportedEncodingException {
		return new HttpHeaders() {{
			  String auth = company + "\\" + username + ":" + password;
			  byte[] encodedAuth = Base64.encodeBase64(auth.getBytes("utf-8"), false);
			  String authHeader = "Basic " + new String( encodedAuth );
			  set( "Authorization", authHeader );
		   }};
	 }

	 private String[] readFromInputStream(InputStream inputStream) throws Exception {
		StringBuilder resultStringBuilder = new StringBuilder();
		String description = "";
		try (BufferedReader br = new BufferedReader(new InputStreamReader(inputStream))) {
			String line;
			while ((line = br.readLine()) != null) {
				if (line.contains("@description")) {
					description = line.substring(line.indexOf("@description") + 12).trim();
				}
				resultStringBuilder.append(line).append("\n");
			}
		}

		String [] results = new String[2];
		results[0] = description;
		results[1] = resultStringBuilder.toString();

		return results;
	}

	private boolean uploadOpnOrFlow(RestTemplate restTemplate, String file)
	{
		File opOrFlow = new File(file);
		if (file.endsWith(".prop.sl")) {
			return true;
		}

		if (!opOrFlow.exists()) {
			return false;
		}

		try {
			String contents[] = readFromInputStream(new FileInputStream(opOrFlow));
			String wfName = file.substring(file.lastIndexOf(File.separator)+1, file.lastIndexOf("."));

			final MultiValueMap<String, String> params = new LinkedMultiValueMap<String, String>();
			String opFlow = file.contains("operation") ? "operation" : "flow";
			params.add("wfType", opFlow);
			params.add("wfName", wfName);
			params.add("wfSubType", "system");
			params.add("wfNamespace", "unvired." + opFlow);
			params.add("wfTitle", wfName.toUpperCase());
			params.add("wfDescription", contents[0]);
			params.add("wfContent", new String(Base64.encodeBase64(contents[1].getBytes("utf-8"), false)));

			HttpHeaders headers = createHeaders(company, user, password);
			headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

			final HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<MultiValueMap<String, String>>(params, headers);
			ResponseEntity<Map>entity = restTemplate.exchange(umpUrl + "API/v2/workflow/"+wfName, HttpMethod.PATCH, request, Map.class);
			if (entity.getStatusCode() != HttpStatus.NO_CONTENT && entity.getStatusCode() != HttpStatus.CREATED) {
				logger.error("Error uploading: " + file + " error: " + entity.getStatusCode() + ((Map)entity.getBody().get("umpResponse")).get("error"));
			}
			else {
				System.out.println("Updated: " + wfName);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return true;
	}
}