{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "Launch LOCALHOST",
            "request": "launch",
            "mainClass": "com.unvired.loader.WorkflowLoader",
            "projectName": "WorkflowLoader",
            "args": "http://localhost:8080/UMP <NAME_EMAIL> \"unvired\" /Users/<USER>/Documents/eclipse-workspace/WorkflowContent/Content/unvired/operation/merge.sl"
        },
        {
            "type": "java",
            "name": "Launch UMPDEV",
            "request": "launch",
            "mainClass": "com.unvired.loader.WorkflowLoader",
            "projectName": "WorkflowLoader",
            "args": "https://umpdev.unvired.io/UMP <NAME_EMAIL> \"unvired\" /Users/<USER>/Documents/eclipse-workspace/WorkflowContent/Content/unvired/operation/odatarequest.sl"
        },
        {
            "type": "java",
            "name": "Launch UMPQA",
            "request": "launch",
            "mainClass": "com.unvired.loader.WorkflowLoader",
            "projectName": "WorkflowLoader",
            "args": "https://umpqa.unvired.io/UMP <NAME_EMAIL> \"unvired\" /Users/<USER>/Documents/eclipse-workspace/WorkflowContent/Content/unvired/flow/formassign.sl"
        },
        {
            "type": "java",
            "name": "Launch UMPPGSQL",
            "request": "launch",
            "mainClass": "com.unvired.loader.WorkflowLoader",
            "projectName": "WorkflowLoader",
            "args": "https://umppg.onunvired.com/UMP <NAME_EMAIL> \"unvired\" /Users/<USER>/Documents/eclipse-workspace/WorkflowContent/Content/unvired/flow/formassign.sl"
        },
        {
            "type": "java",
            "name": "Launch UMPHANA",
            "request": "launch",
            "mainClass": "com.unvired.loader.WorkflowLoader",
            "projectName": "WorkflowLoader",
            "args": "https://umphana.onunvired.com/UMP <NAME_EMAIL> \"unvired\" /Users/<USER>/Documents/eclipse-workspace/WorkflowContent/Content/unvired//flow/formassign.sl"
        },
        {
            "type": "java",
            "name": "Launch UMP-Century-Dev",
            "request": "launch",
            "mainClass": "com.unvired.loader.WorkflowLoader",
            "projectName": "WorkflowLoader",
            "args": "https://9ba9-106-51-168-140.ngrok-free.app/UMP/ custom CENTURY VARANASIR \"1vd1dy8447fa6mle7xgodirysk9r2bp8bipkf7u4\" /Users/<USER>/Documents/eclipse-workspace/WorkflowContent/Content/unvired/operation/condition.sl"
        },
        {
            "type": "java",
            "name": "Launch UMP-Century-PROD",
            "request": "launch",
            "mainClass": "com.unvired.loader.WorkflowLoader",
            "projectName": "WorkflowLoader",
            "args": "https://e72c-106-51-217-108.ngrok-free.app//UMP/ standard CENTURY VARANASIR \"wiaxzdptylpd4dmtev1qn6roit0wqpm14x9n5pw7\" /Users/<USER>/Documents/eclipse-workspace/WorkflowContent/Content/unvired/"
        },
        {
            "type": "java",
            "name": "Launch SBOX",
            "request": "launch",
            "mainClass": "com.unvired.loader.WorkflowLoader",
            "projectName": "WorkflowLoader",
            "args": "https://sandbox.unvired.io/UMP <NAME_EMAIL> \"Unvired123*\" /Users/<USER>/Documents/eclipse-workspace/WorkflowContent/Content/unvired/flow/formassign.sl"
        },
        {
            "type": "java",
            "name": "Launch Production",
            "request": "launch",
            "mainClass": "com.unvired.loader.WorkflowLoader",
            "projectName": "WorkflowLoader",
            "args": "https://live1.unvired.io/UMP <NAME_EMAIL> \"Ssrini22**\" /Users/<USER>/Documents/eclipse-workspace/WorkflowContent/Content/unvired"
        },
        {
            "type": "java",
            "name": "Launch TMS QUALITY",
            "request": "launch",
            "mainClass": "com.unvired.loader.WorkflowLoader",
            "projectName": "WorkflowLoader",
            "args": "https://apitest.tms1st.com/UMP <NAME_EMAIL> \"Unvired321*\" /Users/<USER>/Documents/eclipse-workspace/WorkflowContent/Content/unvired"
        },
        {
            "type": "java",
            "name": "Launch GCP",
            "request": "launch",
            "mainClass": "com.unvired.loader.WorkflowLoader",
            "projectName": "WorkflowLoader",
            "args": "https://sbox.unvired.io/UMP <NAME_EMAIL> \"Unvired321*\" /Users/<USER>/Documents/eclipse-workspace/WorkflowContent/Content/unvired"
        }
    ]
}